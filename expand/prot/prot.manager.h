/*
 * Copyright [2025] MINIEYE
 * Descripttion : 文件描述
 * Author       : xu<PERSON>esen
 * Date         : 2025-03-18
 */

#ifndef __MINIEYE_PROTO__MANAGER_H__
#define __MINIEYE_PROTO__MANAGER_H__

#include "protocol.h"

#include "speedled.h"
#include "tahsensor.h"
#include "testProt.h"
#include "alertor.h"
#include "weightSensor.h"
#include "tpms.haoyue.h"
#include "displed.h"
#include "FuelMeter.ChangRun.h"
#include "AlinkAlertor.RongSheng.h"
#include "LongAnAlertor.RongSheng.h"
#include "BsdCanInfo.RongSheng.h"
#include "AlgoCanDisplay.h"
#include "DispCiBei.h"
#include "XiaMenShuoQi.CanProt.h"

namespace minieye
{

namespace protmanager
{

static Protocol* findProto(string &name)
{
    std::unordered_map<std::string, ProtConstructorFn> protTbl = {
        {"alertor",     protConstructor<Alertor>},
        {"displed",     protConstructor<dispLed>},
        {"speedled",    protConstructor<SpeedLed>},
        {"testprot",    protConstructor<TestProt>},
        {"tpms_haoyue", protConstructor<TpmsHaoyue>},
        {"tahsensor",   protConstructor<TahSensor>},
        {"weightsensor_SAHX_120B",  protConstructor<weightSensor_SAHX_120B>},
        {"weightsensor_ZZH_201",    protConstructor<weightSensor_ZZH_201>},
        {"weightSensor_ZHF03",    protConstructor<weightSensor_ZHF03>},
        {"fuel_changrun",   protConstructor<FuelMeterChangRun>},
        {"fuel_changrun2",   protConstructor<FuelMeterChangRun2nd>},
        {"alinkAlertor_rongsheng", protConstructor<AlinkAlertor>},
        {"longanAlertor_rongsheng", protConstructor<LongAnAlertor>},
        {"bsdCanInfo_rongsheng", protConstructor<BsdCanInfo>},
        {"algoCanDisplay", protConstructor<AlgoCanDisplay>},
        {"DispCiBei", protConstructor<DispCiBei>},
        {"xiaMenShuoQi_CanProt", protConstructor<XiaMenShuoQiCanProt>},
    };

    auto algoProtNameList = {
        "alinkAlertor_rongsheng", "longanAlertor_rongsheng", "bsdCanInfo_rongsheng", "algoCanDisplay"};

    auto pc = protTbl.find(name);
    if (pc != protTbl.end()) {
        for (auto algoProtName : algoProtNameList) {
            if (algoProtName == name) {
                AlgoManager& am = AlgoManager::getInstance();
                am.start();
            }
        }
        return pc->second();
    }
    return nullptr;
}
}  // namespace protmanager

}  // namespace minieye

#endif
