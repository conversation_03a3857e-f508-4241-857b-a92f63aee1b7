/*
 * Copyright [2025] MINIEYE
 * Descripttion : 文件描述
 * Author       : x<PERSON><PERSON><PERSON>n
 * Date         : 2024-10-28
 */
#ifndef __IDVR_EXPAND_RECEIVER_H__
#define __IDVR_EXPAND_RECEIVER_H__


#include "mystd.h"
#include "expand.message.h"
#include "libflow.h"


#define EXPAND_DEV_STATUS_TIMEOUT (-100)
class ExpandReceiver :
    public my::Singleton<ExpandReceiver>, public ILibFlowCallback
{
    friend class my::Singleton<ExpandReceiver>;
    
public:
    int32_t start();
    int32_t stop();
    int32_t getTahSensorMessageRaw(my::string &rawData);
    int32_t getTahSensorMessage(std::vector<double> &temps, std::vector<double> &humis);
    int32_t getWeightSensorMessage(bool &status, bool& isCalib, uint32_t& weightAD, uint32_t &weight, uint16_t &unit);
    int32_t getWeightSensorZHF03Message(bool &status, uint8_t& version, uint16_t& torque, 
                                                        uint32_t &weight, uint16_t &unit, std::vector<uint8_t> &raw);
    int32_t getTpmsSensorMessage(int & tempHiThrs, float & tyrePressThrsHi, float & tyrePressThrsLo,
            std::map<int, float> & tyreTemps, std::map<int, float> & tyrePresses);
    bool getFuelInfo(float & height, int32_t idx = 1);

    void setDataCallback(std::function<void(msgpack::object)> cb) {
        mCallback = cb;
    }

private:
    // 处理expand数据
    void OnLibFlowCallback(const char *source,  // '\0' terminated string
                           const char *topic,   // any binary data
                           const char *data,    // any binary data
                           size_t size);        // < 2^32

private:
    std::mutex mMutex;
    bool started;
    LibflowClient * mLibflowClient; // for getting algo result&msg

    std::function<void(msgpack::object)> mCallback = nullptr;

    expand::TAHSensorMessage mTahSensorMessage;
    std::mutex mTahSensorMessageMutex;

    expand::weightSensorMessage mWeightSensorMessage;
    std::mutex mWeightSensorMessageMutex;

    expand::weightSensorZHF03Message mWeightSensorZhf03Message;
    std::mutex mWeightSensorZhf03MessageMutex;

    expand::TpmsSensorMessage mTpmsSensorMsg;
    std::mutex mTpmsSensorMsgMtx;

    std::map<int32_t, expand::FuelMeterSensorMessage> mFuelSensorMsg;
    std::mutex mFuelSensorMsgMtx;

    expand::XiaMenShouQiMsg mXiaMenShouQiMsg;
    std::mutex mXiaMenShouQiMsgMtx;

};


#endif
