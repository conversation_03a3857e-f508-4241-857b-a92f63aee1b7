#include <sys/system_properties.h>

#include "expand.receiver.h"
#define PROP_PERSIST_FUEL_HEIGHT                                    "persist.fuel%d.height"

SINGLETON_STATIC_INSTANCE(ExpandReceiver);


int32_t ExpandReceiver::start()
{
    std::lock_guard<std::mutex> lock(mMutex);

    if (started) {
        logw("ExpandReceiver already started!");
        return -1;
    }

    for (int32_t i = 1; i <= 2; i++) {
        char propName[128] = {0};
        snprintf(propName, sizeof(propName), PROP_PERSIST_FUEL_HEIGHT, i);
        char pv[PROP_VALUE_MAX] = {0};
        if (__system_property_get(PROP_PERSIST_FUEL_HEIGHT, pv) > 0) {
            mFuelSensorMsg[i].type = i;
            mFuelSensorMsg[i].fuelHeight = atof(pv);
        }
    }
    // libflow初始化
    mLibflowClient = new LibflowClient("127.0.0.1", EXPAND_LIBFLOW_PORT, "*");

    if (mLibflowClient == NULL || mLibflowClient->start(this) != 0) {
        loge("ExpandReceiver start fail!, %s:%s\n", "127.0.0.1", EXPAND_LIBFLOW_PORT);
        return 2;
    }

    started = true;
    return 0;

}


int32_t ExpandReceiver::stop()
{
    std::lock_guard<std::mutex> lock(mMutex);

    if (started == false) {
        logw("ExpandReceiver already stoped");
        return -1;
    }

    // 释放libflow client
    if (mLibflowClient) {
        mLibflowClient->stop();
        free(mLibflowClient);
        mLibflowClient = NULL;
    }

    started = false;

    return 0;
}

void ExpandReceiver::OnLibFlowCallback(const char *source,  // '\0' terminated string
                                       const char *topic,   // any binary data
                                       const char *data,    // any binary data
                                       size_t size)        // < 2^32
{
    if (!strcmp(topic, TAHSENSOR_LIBFLOW_TOPIC)) {  // 处理语音消息
        std::lock_guard<std::mutex> lock(mTahSensorMessageMutex);

        msgpack::object_handle unpack = msgpack::unpack(data, size);
        msgpack::object  obj = unpack.get();
        mTahSensorMessage  = obj.as<expand::TAHSensorMessage>();
        //logi("%s %ld, %f %f, %d, %p", mTahSensorMessage.mType.c_str(), mTahSensorMessage.mUtcTime, mTahSensorMessage.mTemps[0], mTahSensorMessage.mHumis[0],
        //mTahSensorMessage.mRawData.size, mTahSensorMessage.mRawData.ptr);

    } else if (!strcmp(topic, WEIGHTSENSOR_LIBFLOW_TOPIIC)) {
        std::lock_guard<std::mutex> lock(mWeightSensorMessageMutex);

        msgpack::object_handle unpack = msgpack::unpack(data, size);
        msgpack::object  obj = unpack.get();
        mWeightSensorMessage  = obj.as<expand::weightSensorMessage>();
        //logd("recv data: weight=%d, unit=%d", mWeightSensorMessage.mWeight, mWeightSensorMessage.mUnit);

    } else if (!strcmp(topic, WEIGHTSENSORZHF03_LIBFLOW_TOPIIC)) {        
        std::lock_guard<std::mutex> lock(mWeightSensorZhf03MessageMutex);
        msgpack::object_handle unpack = msgpack::unpack(data, size);
        msgpack::object  obj = unpack.get();
        mWeightSensorZhf03Message  = obj.as<expand::weightSensorZHF03Message>();
        #if 0
        logd("recv data: weight=%d, unit=%d,mTorque:%d,mVersion:%d,mStatus:%d!\n", 
                mWeightSensorZhf03Message.mWeight, mWeightSensorZhf03Message.mUnit,
                mWeightSensorZhf03Message.mTorque, mWeightSensorZhf03Message.mVersion, mWeightSensorZhf03Message.mStatus);
        #endif
    }   else if (!strcmp(topic, TPMS_LIBFLOW_TOPIC)) {
        std::lock_guard<std::mutex> lock(mTpmsSensorMsgMtx);
        msgpack::object_handle unpack = msgpack::unpack(data, size);
        msgpack::object  obj = unpack.get();
        mTpmsSensorMsg = obj.as<expand::TpmsSensorMessage>();

        logd("TPMS : UTC %ld", mTpmsSensorMsg.mUtcTime);
        logd("TPMS : tyreNum %d, threshold <tempHi %d, pressHi %f, pressLo %f>",
             mTpmsSensorMsg.tyreNum, mTpmsSensorMsg.tempHiThrs, mTpmsSensorMsg.pressHiThrs, mTpmsSensorMsg.pressLoThrs);

        for (auto it : mTpmsSensorMsg.tyrePressure) {
            logd("\t | Tyre#%d : press %f", it.first, it.second);
        }

        for (auto it : mTpmsSensorMsg.tyreTemperature) {
            logd("\t | Tyre#%d : temp  %f", it.first, it.second);
        }
	} else if (!strcmp(topic, FUEL_LIBFLOW_RAW_TOPIC)) {
        std::lock_guard<std::mutex> lock(mFuelSensorMsgMtx);
        msgpack::object_handle unpack = msgpack::unpack(data, size);
        msgpack::object  obj = unpack.get();
        expand::FuelMeterSensorMessage tmp = obj.as<expand::FuelMeterSensorMessage>();
        mFuelSensorMsg[tmp.type] = tmp;
    } else if (!strcmp(topic, XIAMEN_SHUOQI_LIBFLOW_TOPIC)) {
        std::lock_guard<std::mutex> lock(mXiaMenShouQiMsgMtx);
        msgpack::object_handle unpack = msgpack::unpack(data, size);
        msgpack::object obj = unpack.get();
        mXiaMenShouQiMsg = obj.as<expand::XiaMenShouQiMsg>();
        if (mCallback != nullptr) {
            mCallback(obj);
        }

    } else {
    }
}



int32_t ExpandReceiver::getTahSensorMessageRaw(my::string &rawData)
{
    int64_t now = my::timestamp::utc_milliseconds();
    std::lock_guard<std::mutex> lock(mTahSensorMessageMutex);

    // 超时60s
    if (now - mTahSensorMessage.mUtcTime > 60 * 1000) {
        return EXPAND_DEV_STATUS_TIMEOUT;
    }

    rawData.assign(mTahSensorMessage.mRawData.ptr, mTahSensorMessage.mRawData.size);


    return 0;
}


int32_t ExpandReceiver::getTahSensorMessage(std::vector<double> &temps, std::vector<double> &humis)
{
    int64_t now = my::timestamp::utc_milliseconds();
    std::lock_guard<std::mutex> lock(mTahSensorMessageMutex);

    // 超时60s
    if (now - mTahSensorMessage.mUtcTime > 60 * 1000) {
        return EXPAND_DEV_STATUS_TIMEOUT;
    }

    temps = mTahSensorMessage.mTemps;
    humis = mTahSensorMessage.mHumis;


    return 0;
}

int32_t ExpandReceiver::getWeightSensorMessage(bool &status, bool& isCalib, uint32_t& weightAD, uint32_t &weight, uint16_t &unit)
{
    int64_t now = my::timestamp::utc_milliseconds();
    std::lock_guard<std::mutex> lock(mWeightSensorMessageMutex);
    // 超时60s
    if (now - mWeightSensorMessage.mUtcTime > 60 * 1000) {
        return EXPAND_DEV_STATUS_TIMEOUT;
    }

    status = mWeightSensorMessage.mStatus;
    isCalib = mWeightSensorMessage.mIsCalib;
    weightAD = mWeightSensorMessage.mWeightAD;
    unit = mWeightSensorMessage.mUnit;
    weight = mWeightSensorMessage.mWeight;
    //logd("status=%d, calib=%d, weight=%d uint=%d, AD=%d", status, isCalib, weight, unit, weightAD);


    return 0;
}

int32_t ExpandReceiver::getWeightSensorZHF03Message(bool &status, uint8_t& version, uint16_t& torque, 
                                                                        uint32_t &weight, uint16_t &unit, std::vector<uint8_t> &raw)
{
    int64_t now = my::timestamp::utc_milliseconds();
    std::lock_guard<std::mutex> lock(mWeightSensorZhf03MessageMutex);
    // 超时60s
    if (now - mWeightSensorZhf03Message.mUtcTime > 60 * 1000) {
        return EXPAND_DEV_STATUS_TIMEOUT;
    }

    status = mWeightSensorZhf03Message.mStatus;
    torque = mWeightSensorZhf03Message.mTorque;
    version = mWeightSensorZhf03Message.mVersion;
    unit = mWeightSensorZhf03Message.mUnit;
    weight = mWeightSensorZhf03Message.mWeight;
    for (int i = 0; i < mWeightSensorZhf03Message.mRaw.size(); i++) {
        raw.push_back(mWeightSensorZhf03Message.mRaw[i]);
    }
    logd("[xyj]weight:%d,mRaw.size:%d!\n", weight, mWeightSensorZhf03Message.mRaw.size());
    //logd("status=%d, calib=%d, weight=%d uint=%d, AD=%d", status, isCalib, weight, unit, weightAD);
    return 0;
}

int32_t ExpandReceiver::getTpmsSensorMessage(int & tempHiThrs, float & tyrePressThrsHi, float & tyrePressThrsLo,
        std::map<int, float> & tyreTemps, std::map<int, float> & tyrePresses)
{
    int64_t now = my::timestamp::utc_milliseconds();
    std::lock_guard<std::mutex> lock(mTpmsSensorMsgMtx);

    // 超时60s
    if (now - mTpmsSensorMsg.mUtcTime > 60 * 1000) {
        return EXPAND_DEV_STATUS_TIMEOUT;
    }

    tempHiThrs = mTpmsSensorMsg.tempHiThrs;
    tyrePressThrsHi = mTpmsSensorMsg.pressHiThrs;
    tyrePressThrsLo = mTpmsSensorMsg.pressLoThrs;
    tyreTemps = mTpmsSensorMsg.tyreTemperature;
    tyrePresses = mTpmsSensorMsg.tyrePressure;

    return 0;
}

bool ExpandReceiver::getFuelInfo(float & height, int32_t idx)
{

    std::lock_guard<std::mutex> lock(mFuelSensorMsgMtx);
    if (mFuelSensorMsg.find(idx) == mFuelSensorMsg.end()) {
        return false;
    }
    height = mFuelSensorMsg[idx].fuelHeight;
    return true;
}

